<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\ProductList;
use LBCDev\Ecommerce\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LivewireProductsRealTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Crear algunos productos de prueba
        Product::factory()->create([
            'name' => 'Test Product 1',
            'price' => 99.99,
            'is_active' => true,
        ]);
        
        Product::factory()->create([
            'name' => 'Test Product 2', 
            'price' => 149.99,
            'is_active' => true,
        ]);
    }

    /**
     * Test que el componente ProductList funciona correctamente
     */
    public function test_product_list_component_renders_correctly()
    {
        Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSee('Test Product 1')
            ->assertSee('Test Product 2');
    }

    /**
     * Test que las acciones de ordenamiento funcionan
     */
    public function test_sorting_actions_work()
    {
        Livewire::test(ProductList::class)
            ->assertSet('sortBy', 'name')
            ->assertSet('sortDirection', 'asc')
            ->call('sortBy', 'price')
            ->assertSet('sortBy', 'price')
            ->assertSet('sortDirection', 'asc');
    }

    /**
     * Test que la búsqueda funciona
     */
    public function test_search_functionality_works()
    {
        Livewire::test(ProductList::class)
            ->set('search', 'Test Product 1')
            ->assertSee('Test Product 1')
            ->assertDontSee('Test Product 2');
    }

    /**
     * Test que el componente funciona con diferentes locales
     */
    public function test_component_works_with_different_locales()
    {
        // Test con español
        app()->setLocale('es');
        Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSee('Test Product 1');

        // Test con inglés
        app()->setLocale('en');
        Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSee('Test Product 1');

        // Test con alemán
        app()->setLocale('de');
        Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSee('Test Product 1');
    }

    /**
     * Test que verifica que los nombres de campos están correctos
     */
    public function test_field_names_are_consistent()
    {
        $component = Livewire::test(ProductList::class);
        
        // Verificar que el sortBy inicial es 'name' (no 'nombre')
        $component->assertSet('sortBy', 'name');
        
        // Verificar que podemos ordenar por 'price' (no 'precio')
        $component->call('sortBy', 'price')
                  ->assertSet('sortBy', 'price');
    }

    /**
     * Test que verifica que no hay errores de Livewire
     */
    public function test_no_livewire_errors()
    {
        $component = Livewire::test(ProductList::class);
        
        // Verificar que no hay errores
        $this->assertEmpty($component->errors());
        
        // Verificar que el componente se renderiza sin problemas
        $html = $component->get('products');
        $this->assertNotNull($html);
    }
}
