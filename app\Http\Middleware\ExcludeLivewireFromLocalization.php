<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ExcludeLivewireFromLocalization
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Si la petición es para una ruta de Livewire, marcarla para que sea ignorada por LaravelLocalization
        if ($this->isLivewireRequest($request)) {
            // Agregar la URL actual a la lista de URLs ignoradas temporalmente
            $currentIgnored = config('laravellocalization.urlsIgnored', []);
            $currentPath = '/' . $request->path();

            if (!in_array($currentPath, $currentIgnored)) {
                $currentIgnored[] = $currentPath;
                config(['laravellocalization.urlsIgnored' => $currentIgnored]);
            }
        }

        return $next($request);
    }

    /**
     * Determinar si la petición es para una ruta de Livewire
     */
    private function isLivewireRequest(Request $request): bool
    {
        $path = $request->path();

        $livewirePaths = [
            'livewire/update',
            'livewire/upload-file',
            'livewire/preview-file',
            'livewire/livewire.js',
            'livewire/livewire.min.js.map',
        ];

        foreach ($livewirePaths as $livewirePath) {
            if (str_starts_with($path, $livewirePath)) {
                return true;
            }
        }

        // También verificar si empieza con 'livewire/'
        if (str_starts_with($path, 'livewire/')) {
            return true;
        }

        return false;
    }
}
