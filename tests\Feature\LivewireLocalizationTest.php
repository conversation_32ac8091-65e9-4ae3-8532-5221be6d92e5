<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\Test\LocalizationTest;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LivewireLocalizationTest extends TestCase
{
    /**
     * Test que el componente de Livewire funciona correctamente
     */
    public function test_livewire_component_can_be_rendered()
    {
        Livewire::test(LocalizationTest::class)
            ->assertSee('Test de Localización Livewire')
            ->assertSee('Componente cargado correctamente');
    }

    /**
     * Test que el componente funciona con diferentes locales
     */
    public function test_livewire_component_works_with_different_locales()
    {
        // Test con español
        app()->setLocale('es');
        Livewire::test(LocalizationTest::class)
            ->assertSet('currentLocale', 'es')
            ->assertSee('es');

        // Test con inglés
        app()->setLocale('en');
        Livewire::test(LocalizationTest::class)
            ->assertSet('currentLocale', 'en')
            ->assertSee('en');

        // Test con alemán
        app()->setLocale('de');
        Livewire::test(LocalizationTest::class)
            ->assertSet('currentLocale', 'de')
            ->assertSee('de');
    }

    /**
     * Test que las acciones de Livewire funcionan correctamente
     */
    public function test_livewire_actions_work_correctly()
    {
        Livewire::test(LocalizationTest::class)
            ->assertSet('counter', 0)
            ->call('increment')
            ->assertSet('counter', 1)
            ->call('decrement')
            ->assertSet('counter', 0)
            ->call('resetCounter')
            ->assertSet('counter', 0);
    }

    /**
     * Test que las rutas de Livewire están excluidas de la localización
     */
    public function test_livewire_routes_are_excluded_from_localization()
    {
        $response = $this->get('/livewire/livewire.js');
        $response->assertStatus(200);

        // Verificar que no se redirige a una URL localizada
        $this->assertStringNotContainsString('/es/livewire', $response->headers->get('location') ?? '');
        $this->assertStringNotContainsString('/en/livewire', $response->headers->get('location') ?? '');
        $this->assertStringNotContainsString('/de/livewire', $response->headers->get('location') ?? '');
    }

    /**
     * Test que la página home con el componente Livewire funciona
     */
    public function test_home_page_with_livewire_component_works()
    {
        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertSee('Test de Livewire');
    }

    /**
     * Test que la página home funciona con diferentes locales
     */
    public function test_home_page_works_with_different_locales()
    {
        // Test con español
        $response = $this->get('/es');
        $response->assertStatus(200);

        // Test con inglés
        $response = $this->get('/en');
        $response->assertStatus(200);

        // Test con alemán
        $response = $this->get('/de');
        $response->assertStatus(200);
    }
}
