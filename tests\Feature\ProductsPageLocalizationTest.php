<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\ProductList;
use LBCDev\Ecommerce\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductsPageLocalizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Crear algunos productos de prueba
        Product::factory()->create([
            'name' => 'Test Product 1',
            'price' => 99.99,
            'is_active' => true,
        ]);

        Product::factory()->create([
            'name' => 'Test Product 2',
            'price' => 149.99,
            'is_active' => true,
        ]);
    }

    /**
     * Test que la página de productos funciona con diferentes locales
     */
    public function test_products_page_works_with_different_locales()
    {
        // Configurar el entorno para que funcione con localización
        config(['app.locale' => 'es']);

        // Test con español - seguir redirecciones
        app()->setLocale('es');
        $response = $this->followingRedirects()->get('/products');
        $response->assertStatus(200);

        // Test con inglés
        app()->setLocale('en');
        $response = $this->followingRedirects()->get('/products');
        $response->assertStatus(200);

        // Test con alemán
        app()->setLocale('de');
        $response = $this->followingRedirects()->get('/products');
        $response->assertStatus(200);
    }

    /**
     * Test que el componente ProductList funciona con localización
     */
    public function test_product_list_component_works_with_localization()
    {
        // Test con español
        app()->setLocale('es');
        Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSee('Test Product 1')
            ->assertSee('Test Product 2');

        // Test con inglés
        app()->setLocale('en');
        Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSee('Test Product 1')
            ->assertSee('Test Product 2');

        // Test con alemán
        app()->setLocale('de');
        Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSee('Test Product 1')
            ->assertSee('Test Product 2');
    }

    /**
     * Test que las acciones de Livewire funcionan en páginas localizadas
     */
    public function test_livewire_actions_work_in_localized_pages()
    {
        app()->setLocale('es');

        Livewire::test(ProductList::class)
            ->assertSet('sortBy', 'name')
            ->assertSet('sortDirection', 'asc')
            ->call('sortBy', 'price')
            ->assertSet('sortBy', 'price')
            ->assertSet('sortDirection', 'asc');
    }

    /**
     * Test que la búsqueda funciona con localización
     */
    public function test_search_works_with_localization()
    {
        app()->setLocale('es');

        Livewire::test(ProductList::class)
            ->set('search', 'Test Product 1')
            ->assertSee('Test Product 1')
            ->assertDontSee('Test Product 2');
    }

    /**
     * Test que las rutas de productos están correctamente localizadas
     */
    public function test_product_routes_are_localized()
    {
        // En el entorno de testing, verificamos que la ruta base funciona
        $response = $this->get('/products');
        $this->assertTrue(in_array($response->getStatusCode(), [200, 302]));

        // Verificar que podemos cambiar el locale y la página sigue funcionando
        app()->setLocale('es');
        $this->followingRedirects()->get('/products')->assertStatus(200);

        app()->setLocale('en');
        $this->followingRedirects()->get('/products')->assertStatus(200);

        app()->setLocale('de');
        $this->followingRedirects()->get('/products')->assertStatus(200);
    }

    /**
     * Test que los enlaces en la página mantienen la localización
     */
    public function test_links_maintain_localization()
    {
        app()->setLocale('es');
        $response = $this->followingRedirects()->get('/products');
        $response->assertStatus(200);

        // Verificar que el componente Livewire se renderiza correctamente
        $content = $response->getContent();
        $this->assertStringContainsString('wire:', $content);
    }

    /**
     * Test específico para verificar que Livewire funciona en la URL problemática
     */
    public function test_livewire_works_on_localized_products_page()
    {
        // Simular una petición a la página de productos en inglés
        app()->setLocale('en');
        $response = $this->followingRedirects()->get('/products');
        $response->assertStatus(200);

        // Verificar que el componente Livewire se renderiza
        $response->assertSee('wire:');
        $response->assertSee('livewire');

        // Verificar que no hay errores de JavaScript o Livewire
        $content = $response->getContent();
        $this->assertStringNotContainsString('Livewire error', $content);
        $this->assertStringNotContainsString('Component not found', $content);
    }
}
