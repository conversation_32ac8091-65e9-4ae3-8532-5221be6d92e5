<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\ProductList;
use LBCDev\Ecommerce\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LivewireAjaxLocalizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Crear algunos productos de prueba
        Product::factory()->create([
            'name' => 'Test Product 1',
            'price' => 99.99,
            'is_active' => true,
        ]);
        
        Product::factory()->create([
            'name' => 'Test Product 2', 
            'price' => 149.99,
            'is_active' => true,
        ]);
    }

    /**
     * Test que las peticiones AJAX de Livewire funcionan desde páginas localizadas
     */
    public function test_livewire_ajax_works_from_localized_pages()
    {
        // Simular que estamos en una página localizada
        app()->setLocale('en');
        
        // Crear el componente como si estuviéramos en /en/products
        $component = Livewire::test(ProductList::class)
            ->assertStatus(200)
            ->assertSet('sortBy', 'name')
            ->assertSet('sortDirection', 'asc');

        // Simular la acción que causa el problema (cambiar ordenamiento)
        $component->call('sortBy', 'price')
            ->assertSet('sortBy', 'price')
            ->assertSet('sortDirection', 'asc');

        // Verificar que no hay errores
        $this->assertEmpty($component->errors());
    }

    /**
     * Test que verifica que las rutas de Livewire están excluidas
     */
    public function test_livewire_routes_are_excluded_from_localization()
    {
        // Verificar que las rutas de Livewire están en la lista de exclusiones
        $urlsIgnored = config('laravellocalization.urlsIgnored');
        
        $this->assertContains('/livewire/*', $urlsIgnored);
        $this->assertContains('/livewire/update', $urlsIgnored);
        $this->assertContains('livewire/*', $urlsIgnored);
        $this->assertContains('livewire/update', $urlsIgnored);
    }

    /**
     * Test que simula una petición AJAX real
     */
    public function test_livewire_update_endpoint_is_accessible()
    {
        // Crear un componente
        $component = Livewire::test(ProductList::class);
        
        // Obtener el snapshot del componente
        $snapshot = $component->snapshot;
        
        // Simular una petición AJAX como la que haría Livewire
        $response = $this->post('/livewire/update', [
            'components' => [
                [
                    'snapshot' => $snapshot,
                    'updates' => [
                        [
                            'type' => 'callMethod',
                            'payload' => [
                                'method' => 'sortBy',
                                'params' => ['price']
                            ]
                        ]
                    ],
                    'calls' => []
                ]
            ]
        ]);

        // Verificar que la petición no devuelve 404
        $this->assertNotEquals(404, $response->getStatusCode());
    }

    /**
     * Test que verifica el comportamiento con diferentes locales
     */
    public function test_livewire_works_with_all_supported_locales()
    {
        $locales = ['es', 'en', 'de'];
        
        foreach ($locales as $locale) {
            app()->setLocale($locale);
            
            $component = Livewire::test(ProductList::class)
                ->assertStatus(200)
                ->call('sortBy', 'price')
                ->assertSet('sortBy', 'price');
                
            $this->assertEmpty($component->errors(), "Errors found for locale: {$locale}");
        }
    }
}
